<?php $__env->startSection('content'); ?>
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title text-center fw-bold py-2">Müşteri Detayı</h3>
                </div>
                <div class="card-body">
                    
                    <!-- <PERSON>lik Bilgileri -->
                    <h5 class="mb-3 text-primary">Kimlik Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">TC Kimlik No:</label>
                                <p class="mb-2"><?php echo e($customer->tc ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">PlusCard No:</label>
                                <p class="mb-2"><?php echo e($customer->pluscard_no ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>
                    

                    <!-- Firma Bilgileri -->
                    <h5 class="mb-3 text-primary">Firma Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Firma Adı:</label>
                                <p class="mb-2"><?php echo e($customer->company_name ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Yetkili Kişi Bilgileri -->
                    <h5 class="mb-3 text-primary">Yetkili Kişi Bilgileri</h5>

                    <?php
                        $allAuthorizedPersons = collect();

                        // Eski tek yetkili kişi bilgilerini ekle (eğer varsa)
                        if($customer->authorized_title || $customer->authorized_first_name || $customer->authorized_last_name || $customer->authorized_phone) {
                            $allAuthorizedPersons->push((object)[
                                'title' => $customer->authorized_title,
                                'first_name' => $customer->authorized_first_name,
                                'last_name' => $customer->authorized_last_name,
                                'phone' => $customer->authorized_phone,
                            ]);
                        }

                        // Yeni yetkili kişileri ekle
                        if($customer->authorizedPersons) {
                            $allAuthorizedPersons = $allAuthorizedPersons->merge($customer->authorizedPersons);
                        }
                    ?>

                    <?php if($allAuthorizedPersons->count() > 0): ?>
                        <?php $__currentLoopData = $allAuthorizedPersons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $person): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-secondary mb-3">Yetkili Kişi #<?php echo e($index + 1); ?></h6>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili Ünvan:</label>
                                        <p class="mb-2"><?php echo e($person->title ?? 'Belirtilmemiş'); ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili Telefon:</label>
                                        <p class="mb-2"><?php echo e($person->phone ?? 'Belirtilmemiş'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili İsim:</label>
                                        <p class="mb-2"><?php echo e($person->first_name ?? 'Belirtilmemiş'); ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label class="fw-bold text-muted">Yetkili Soyisim:</label>
                                        <p class="mb-2"><?php echo e($person->last_name ?? 'Belirtilmemiş'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <?php if(!$loop->last): ?>
                                <hr class="my-4">
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="info-group">
                                    <p class="mb-2 text-muted">Yetkili kişi bilgisi bulunmamaktadır.</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- İletişim Bilgileri -->
                    <h5 class="mb-3 text-primary">İletişim Bilgileri</h5>

                    <!-- Şirket Telefonları -->
                    <div class="mb-4">
                        <h6 class="mb-3 text-secondary">Şirket Telefonları</h6>
                        <?php
                            $allPhones = collect();

                            // Eski telefon alanlarından veri al (eğer yeni sistem henüz aktif değilse)
                            if($customer->phone_1) {
                                $allPhones->push((object)[
                                    'phone' => $customer->phone_1,
                                    'type' => 'Sabit'
                                ]);
                            }
                            if($customer->phone_2) {
                                $allPhones->push((object)[
                                    'phone' => $customer->phone_2,
                                    'type' => 'Sabit'
                                ]);
                            }
                            if($customer->phone_3) {
                                $allPhones->push((object)[
                                    'phone' => $customer->phone_3,
                                    'type' => 'Sabit'
                                ]);
                            }

                            // Yeni sistemden telefonları al (eğer ilişki mevcutsa)
                            if(method_exists($customer, 'phones') && $customer->phones) {
                                $newPhones = $customer->phones;
                                foreach($newPhones as $phone) {
                                    $allPhones->push($phone);
                                }
                            }
                        ?>

                        <?php if($allPhones->count() > 0): ?>
                            <div class="row">
                                <?php $__currentLoopData = $allPhones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $phone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card border-start border-primary border-3 h-100 shadow-sm">
                                            <div class="card-body py-3">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div class="flex-grow-1">
                                                        <h6 class="text-primary mb-2">
                                                            <i class="fas fa-phone me-2"></i>Telefon #<?php echo e($index + 1); ?>

                                                        </h6>
                                                        <p class="mb-1 fw-bold fs-6">
                                                            <a href="tel:<?php echo e($phone->phone); ?>" class="text-decoration-none text-dark">
                                                                <?php echo e($phone->phone); ?>

                                                            </a>
                                                        </p>
                                                        <small class="text-muted">
                                                            <i class="fas fa-tag me-1"></i><?php echo e($phone->type); ?>

                                                        </small>
                                                    </div>
                                                    <div class="text-end">
                                                        <span class="badge bg-<?php echo e($phone->type == 'Mobil' ? 'success' : ($phone->type == 'Fax' ? 'warning' : ($phone->type == 'Diğer' ? 'info' : 'primary'))); ?> mb-2">
                                                            <?php echo e($phone->type); ?>

                                                        </span>
                                                        <br>
                                                        <a href="tel:<?php echo e($phone->phone); ?>" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-phone"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php if(($index + 1) % 2 == 0): ?>
                                        </div><div class="row">
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Henüz şirket telefonu eklenmemiş.
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">E-posta:</label>
                                <p class="mb-2"><?php echo e($customer->email ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Adres Bilgileri -->
                    <h5 class="mb-3 text-primary">Adres Bilgileri</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">İl:</label>
                                <p class="mb-2"><?php echo e($customer->city ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold text-muted">İlçe:</label>
                                <p class="mb-2"><?php echo e($customer->district ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="info-group">
                                <label class="fw-bold text-muted">Adres:</label>
                                <p class="mb-2"><?php echo e($customer->address ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Müşteri Resimleri -->
                    <?php if($customer->images && $customer->images->count() > 0): ?>
                        <h5 class="mb-3 text-primary">Müşteri Resimleri</h5>
                        <div class="row mb-4">
                            <?php $__currentLoopData = $customer->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                
                                <div class="col-12 mb-2">
                                    <small class="text-muted">
                                        Debug - Path: <?php echo e($image->path); ?> | URL: <?php echo e($image->url); ?>

                                    </small>
                                </div>
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <div class="card h-100">
                                        <div class="position-relative">
                                            <img src="<?php echo e($image->url); ?>"
                                                 class="card-img-top"
                                                 alt="<?php echo e($image->alt_text ?? $image->filename); ?>"
                                                 style="height: 200px; object-fit: cover; cursor: pointer; transition: transform 0.2s;"
                                                 onclick="showImageModal('<?php echo e($image->url); ?>', '<?php echo e($image->filename); ?>')"
                                                 onmouseover="this.style.transform='scale(1.05)'"
                                                 onmouseout="this.style.transform='scale(1)'"
                                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgNzVMMTI1IDEwMEgxMTJWMTI1SDg4VjEwMEg3NUwxMDAgNzVaIiBmaWxsPSIjOUM5Qzk5Ii8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTUwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUM5Qzk5IiBmb250LXNpemU9IjEyIj5SZXNpbSBZw7xrbGVuZW1lZGk8L3RleHQ+Cjwvc3ZnPg=='; this.style.cursor='default'; this.onclick=null; this.onmouseover=null; this.onmouseout=null;">
                                            <div class="position-absolute top-0 end-0 m-2">
                                                <span class="badge bg-dark bg-opacity-75">
                                                    <i class="fas fa-search-plus"></i>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="card-body p-2">
                                            <p class="card-text small mb-1">
                                                <strong><?php echo e(Str::limit($image->filename, 20)); ?></strong>
                                            </p>
                                            <p class="card-text small text-muted mb-0">
                                                <?php echo e($image->formatted_size); ?> • <?php echo e(strtoupper(pathinfo($image->filename, PATHINFO_EXTENSION))); ?>

                                            </p>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>

                </div>
                <div class="card-footer d-flex gap-2">
                    <a href="<?php echo e(route('customers.edit', $customer->id)); ?>" class="btn btn-primary flex-fill">Düzenle</a>
                    <a href="<?php echo e(route('customers.customer-followups.create', $customer->id)); ?>" class="btn btn-success flex-fill">Takip Ekle</a>
                    <a href="<?php echo e(route('customers.index')); ?>" class="btn btn-secondary flex-fill">Geri Dön</a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-group {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-bottom: 10px;
}

.info-group label {
    font-size: 0.9rem;
    margin-bottom: 5px;
    display: block;
}

.info-group p {
    font-size: 1rem;
    margin: 0;
    color: #333;
}

.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
}
</style>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Resim Görüntüle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid" alt="">
            </div>
        </div>
    </div>
</div>

<script>
function showImageModal(imageUrl, filename) {
    console.log('Image URL:', imageUrl); // Debug için
    document.getElementById('modalImage').src = imageUrl;
    document.getElementById('imageModalLabel').textContent = filename;
    new bootstrap.Modal(document.getElementById('imageModal')).show();
}

// Sayfa yüklendiğinde tüm resim URL'lerini kontrol et
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('.card-img-top');
    images.forEach((img, index) => {
        console.log(`Image ${index + 1} URL:`, img.src);

        // Resim yüklenme durumunu kontrol et
        img.onload = function() {
            console.log(`Image ${index + 1} loaded successfully`);
        };

        img.onerror = function() {
            console.log(`Image ${index + 1} failed to load:`, img.src);
        };
    });
});
</script>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/customers/show.blade.php ENDPATH**/ ?>
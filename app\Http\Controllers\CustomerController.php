<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\CustomerPhone;
use Illuminate\Http\Request;

class CustomerController extends Controller
{
    public function index()
    {
        $perPage = request('perPage', 10);
        $customers = \App\Models\Customer::with(['phones', 'authorizedPersons'])
            ->search(request('q'))
            ->orderBy('id', 'desc')
            ->paginate($perPage)
            ->appends(request()->query());
        return view('customers.index', compact('customers'));
    }

    public function create()
    {
        return view('customers.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email|max:100|unique:customers,email',
            'tc' => 'nullable|string|max:11|unique:customers,tc',
            'pluscard_no' => 'nullable|string|max:40|unique:customers,pluscard_no',
            'authorized_title' => 'nullable|string|min:2|max:255',
            'authorized_first_name' => 'nullable|string|min:2|max:255',
            'authorized_last_name' => 'nullable|string|min:2|max:255',
            'authorized_phone' => 'nullable|string|min:10|max:20',
            'authorized_persons' => 'nullable|array',
            'authorized_persons.*.title' => 'nullable|string|max:255',
            'authorized_persons.*.first_name' => 'nullable|string|max:255',
            'authorized_persons.*.last_name' => 'nullable|string|max:255',
            'authorized_persons.*.phone' => 'nullable|string|max:20',
            'company_phones' => 'nullable|array',
            'company_phones.*.phone' => 'nullable|string|max:20',
            'company_phones.*.type' => 'nullable|string|in:Sabit,Mobil,Fax,Diğer',
            'company_name' => 'nullable|string|max:255',
            'phone_1' => 'nullable|string|max:255',
            'phone_2' => 'nullable|string|max:255',
            'phone_3' => 'nullable|string|max:255',
            'city' => 'required|string|min:2|max:50',
            'district' => 'required|string|min:2|max:50',
            'address' => 'required|string|min:2|max:255',
        ]);

        $authorizedPersons = $validated['authorized_persons'] ?? [];
        $companyPhones = $validated['company_phones'] ?? [];
        unset($validated['authorized_persons']);
        unset($validated['company_phones']);

        $customer = \App\Models\Customer::create($validated);

        // Yetkili kişileri ekle
        foreach ($authorizedPersons as $person) {
            if (!empty(trim($person['first_name'] ?? '')) ||
                !empty(trim($person['last_name'] ?? '')) ||
                !empty(trim($person['title'] ?? '')) ||
                !empty(trim($person['phone'] ?? ''))) {

                $customer->authorizedPersons()->create([
                    'title' => trim($person['title'] ?? ''),
                    'first_name' => trim($person['first_name'] ?? ''),
                    'last_name' => trim($person['last_name'] ?? ''),
                    'phone' => trim($person['phone'] ?? ''),
                ]);
            }
        }

        // Şirket telefonlarını ekle
        foreach ($companyPhones as $phone) {
            if (!empty(trim($phone['phone'] ?? ''))) {
                $customer->phones()->create([
                    'phone' => trim($phone['phone']),
                    'type' => trim($phone['type'] ?? 'Sabit'),
                ]);
            }
        }

        return redirect()->route('customers.index')->with('success', 'Müşteri başarıyla eklendi!');
    }

    public function show($id)
    {
        $customer = \App\Models\Customer::with(['phones', 'authorizedPersons'])->findOrFail($id);
        return view('customers.show', compact('customer'));
    }

    public function edit($id)
    {
        $customer = \App\Models\Customer::with(['phones', 'authorizedPersons'])->findOrFail($id);
        return view('customers.edit', compact('customer'));
    }

    public function update(Request $request, $id)
    {
        $customer = \App\Models\Customer::findOrFail($id);
        $validated = $request->validate([
            'email' => 'required|email|max:100|unique:customers,email,' . $customer->id,
            'tc' => 'nullable|string|max:11|unique:customers,tc,' . $customer->id,
            'pluscard_no' => 'nullable|string|max:40|unique:customers,pluscard_no,' . $customer->id,
            'authorized_title' => 'nullable|string|min:2|max:255',
            'authorized_first_name' => 'nullable|string|min:2|max:255',
            'authorized_last_name' => 'nullable|string|min:2|max:255',
            'authorized_phone' => 'nullable|string|min:10|max:20',
            'authorized_persons' => 'nullable|array',
            'authorized_persons.*.title' => 'nullable|string|max:255',
            'authorized_persons.*.first_name' => 'nullable|string|max:255',
            'authorized_persons.*.last_name' => 'nullable|string|max:255',
            'authorized_persons.*.phone' => 'nullable|string|max:20',
            'company_phones' => 'nullable|array',
            'company_phones.*.phone' => 'nullable|string|max:20',
            'company_phones.*.type' => 'nullable|string|in:Sabit,Mobil,Fax,Diğer',
            'company_phones.*.id' => 'nullable|integer|exists:customer_phones,id',
            'company_name' => 'nullable|string|max:255',
            'phone_1' => 'nullable|string|max:255',
            'phone_2' => 'nullable|string|max:255',
            'phone_3' => 'nullable|string|max:255',
            'city' => 'required|string|min:2|max:50',
            'district' => 'required|string|min:2|max:50',
            'address' => 'required|string|min:2|max:255',
        ]);

        $authorizedPersons = $validated['authorized_persons'] ?? [];
        $companyPhones = $validated['company_phones'] ?? [];
        unset($validated['authorized_persons']);
        unset($validated['company_phones']);

        $customer->update($validated);

        // Mevcut yetkili kişileri sil ve yenilerini ekle
        $customer->authorizedPersons()->delete();

        foreach ($authorizedPersons as $person) {
            if (!empty(trim($person['first_name'] ?? '')) ||
                !empty(trim($person['last_name'] ?? '')) ||
                !empty(trim($person['title'] ?? '')) ||
                !empty(trim($person['phone'] ?? ''))) {

                $customer->authorizedPersons()->create([
                    'title' => trim($person['title'] ?? ''),
                    'first_name' => trim($person['first_name'] ?? ''),
                    'last_name' => trim($person['last_name'] ?? ''),
                    'phone' => trim($person['phone'] ?? ''),
                ]);
            }
        }

        // Şirket telefonlarını güncelle
        $existingPhoneIds = [];
        foreach ($companyPhones as $phone) {
            if (!empty(trim($phone['phone'] ?? ''))) {
                if (!empty($phone['id'])) {
                    // Mevcut telefonu güncelle
                    $existingPhone = $customer->phones()->find($phone['id']);
                    if ($existingPhone) {
                        $existingPhone->update([
                            'phone' => trim($phone['phone']),
                            'type' => trim($phone['type'] ?? 'Sabit'),
                        ]);
                        $existingPhoneIds[] = $phone['id'];
                    }
                } else {
                    // Yeni telefon ekle
                    $newPhone = $customer->phones()->create([
                        'phone' => trim($phone['phone']),
                        'type' => trim($phone['type'] ?? 'Sabit'),
                    ]);
                    $existingPhoneIds[] = $newPhone->id;
                }
            }
        }

        // Formda olmayan telefonları sil
        $customer->phones()->whereNotIn('id', $existingPhoneIds)->delete();

        return redirect()->route('customers.index')->with('success', 'Müşteri başarıyla güncellendi!');
    }

    public function createFromPotential($potentialCustomerId)
    {
        $potential = \App\Models\PotentialCustomer::with(['authorizedPersons', 'phones'])->findOrFail($potentialCustomerId);

        // Ana alan eşleştirmesi
        $prefill = [
            'email' => $potential->email ?? '',
            'company_name' => $potential->company_name ?? '',
            'pluscard_no' => $potential->pluscard_no ?? '',
            'tc' => $potential->tc ?? '',
            'authorized_title' => $potential->authorized_title ?? '',
            'authorized_first_name' => $potential->authorized_name ?? $potential->authorized_first_name,
            'authorized_last_name' => $potential->authorized_lastname ?? $potential->authorized_last_name,
            'authorized_phone' => $potential->authorized_phone ?? '',
            'phone_1' => $potential->phone_1 ?? '',
            'phone_2' => $potential->phone_2 ?? '',
            'phone_3' => $potential->phone_3 ?? '',
            'city' => $potential->city ?? '',
            'district' => $potential->district ?? '',
            'address' => $potential->address ?? '',
        ];

        // Yetkili kişileri hazırla (PotentialCustomer'da name/lastname, Customer'da first_name/last_name)
        $authorizedPersons = [];
        foreach ($potential->authorizedPersons as $person) {
            $authorizedPersons[] = [
                'title' => $person->title ?? '',
                'first_name' => $person->name ?? '', // name -> first_name
                'last_name' => $person->lastname ?? '', // lastname -> last_name
                'phone' => $person->phone ?? '',
            ];
        }

        // Şirket telefonlarını hazırla
        $companyPhones = [];
        foreach ($potential->phones as $phone) {
            $companyPhones[] = [
                'phone' => $phone->phone ?? '',
                'type' => $phone->type ?? 'Sabit',
            ];
        }

        return view('customers.create', compact('prefill', 'authorizedPersons', 'companyPhones'));
    }

    public function registrySearch(Request $request)
    {
        $q = trim($request->input('q'));

        $customer = Customer::where('tc', $q)
            ->orWhere('pluscard_no', $q)
            ->first();

        if ($customer) {
            return response()->json($customer);
        } else {
            return response()->json(['message' => 'Kayıt bulunamadı'], 404);
        }
    }
} 
<?php

use App\Http\Controllers\HomeController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\PotentialCustomerController;

Route::middleware('auth')->group(function () {
    Route::get('/dashboard',[DashboardController::class,'index'])->name('dashboard');
    Route::get('/',[HomeController::class,'index'])->name('index');
    Route::get('/umrandev',[HomeController::class,'umrandev'])->name('umrandev');
    Route::get('/musteriler/index', [CustomerController::class, 'index'])->name('customers.index');
    Route::get('/musteriler/create', [CustomerController::class, 'create'])->name('customers.create');
    Route::post('/musteriler', [CustomerController::class, 'store'])->name('customers.store');
    Route::get('/musteriler/registrySearch', [CustomerController::class, 'registrySearch'])->name('customers.registrySearch');
    Route::get('/musteriler/{customer}', [CustomerController::class, 'show'])->name('customers.show');
    Route::get('/musteriler/{customer}/edit', [CustomerController::class, 'edit'])->name('customers.edit');
    Route::put('/musteriler/{customer}', [CustomerController::class, 'update'])->name('customers.update');
    Route::delete('/customers/images/{image}', [CustomerController::class, 'deleteImage'])->name('customers.images.delete');
    Route::resource('potential-customers', PotentialCustomerController::class);
    Route::resource('customers.customer-followups', \App\Http\Controllers\CustomerFollowupController::class);
    Route::get('/customer-followups', [\App\Http\Controllers\CustomerFollowupController::class, 'allFollowups'])->name('customer-followups.all');
    Route::get('/musteriler/create-from-potential/{potentialCustomer}', [CustomerController::class, 'createFromPotential'])->name('customers.createFromPotential');
});

Route::get('login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('login', [AuthController::class, 'login']);
Route::post('logout', [AuthController::class, 'logout'])->name('logout');
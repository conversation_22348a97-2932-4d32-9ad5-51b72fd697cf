<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('images', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->string('filename'); // Orijinal dosya adı
            $table->string('path'); // Dosyanın storage'daki yolu
            $table->string('mime_type'); // Dosya tipi (image/jpeg, image/png, vb.)
            $table->integer('size'); // Dosya boyutu (bytes)
            $table->string('alt_text')->nullable(); // Alternatif metin
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('images');
    }
};

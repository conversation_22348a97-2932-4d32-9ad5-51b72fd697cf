<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class Image extends Model
{
    protected $fillable = [
        'customer_id',
        'filename',
        'path',
        'mime_type',
        'size',
        'alt_text',
    ];

    /**
     * Customer ile ilişki
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Resmin tam URL'ini döndür
     */
    public function getUrlAttribute(): string
    {
        return Storage::url($this->path);
    }

    /**
     * Dosya boyutunu human readable formatta döndür
     */
    public function getFormattedSizeAttribute(): string
    {
        $bytes = $this->size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Resmin thumbnail URL'ini döndür (gelecekte kullanım için)
     */
    public function getThumbnailUrlAttribute(): string
    {
        // Şimdilik orijinal resmi döndür, gelecekte thumbnail oluşturma eklenebilir
        return $this->url;
    }
}
